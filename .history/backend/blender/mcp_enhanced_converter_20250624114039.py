"""
MCP Enhanced Converter
Integrates MCP tools with the existing action conversion workflow
"""

import json
import math
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

from backend.blender.converter import GameActionToBlenderConverter
from backend.blender.mcp_server import get_mcp_manager, MCPServerManager
from backend.blender.animation_schema import (
    BlenderAnimationClip, BlenderAnimationChannel, BlenderKeyframe,
    BlenderProject, BlenderScene, BlenderExportSettings,
    BlenderAnimationType, InterpolationMode,
    seconds_to_frames, FRAME_RATE
)
from backend.utils.file import get_output_file_path


class MCPEnhancedConverter(GameActionToBlenderConverter):
    """MCP增强的游戏动作转换器"""
    
    def __init__(self, character_name: str = "Character", frame_rate: float = FRAME_RATE,
                 use_mcp: bool = True):
        """初始化MCP增强转换器
        
        Args:
            character_name: 角色名称
            frame_rate: 帧率
            use_mcp: 是否使用MCP功能
        """
        super().__init__(character_name, frame_rate)
        self.use_mcp = use_mcp
        self.mcp_manager: Optional[MCPServerManager] = None
        self.scene_info: Optional[Dict[str, Any]] = None
        
        if self.use_mcp:
            try:
                self.mcp_manager = get_mcp_manager()
                if self.mcp_manager.is_server_running():
                    logger.info("MCP服务器已连接，启用增强功能")
                    self._initialize_scene()
                else:
                    logger.warning("MCP服务器未运行，使用基础转换功能")
                    self.use_mcp = False
            except Exception as e:
                logger.warning(f"MCP初始化失败，使用基础转换功能: {e}")
                self.use_mcp = False
    
    def _initialize_scene(self):
        """初始化Blender场景"""
        if not self.mcp_manager:
            return
            
        try:
            # 获取当前场景信息
            self.scene_info = self.mcp_manager.get_scene_info()
            logger.info(f"获取到场景信息: {self.scene_info.get('name', 'Unknown')}")
            
            # 清理场景并创建基础角色
            cleanup_code = f"""
import bpy

# 清理现有网格对象
bpy.ops.object.select_all(action='DESELECT')
bpy.ops.object.select_by_type(type='MESH')
bpy.ops.object.delete(use_global=False)

# 创建基础角色
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 1))
character = bpy.context.active_object
character.name = "{self.character_name}"

# 设置基础材质
mat = bpy.data.materials.new(name="{self.character_name}_Material")
mat.use_nodes = True
mat.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (0.8, 0.6, 0.4, 1.0)
character.data.materials.append(mat)

print(f"角色 {{character.name}} 创建完成")
"""
            
            result = self.mcp_manager.execute_blender_code(cleanup_code)
            if result.get("success"):
                logger.info("场景初始化完成")
            else:
                logger.warning(f"场景初始化失败: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"场景初始化异常: {e}")
    
    def convert_actions_to_blender(self, actions: List[Dict[str, Any]],
                                 project_name: str = "MotionAgentAnimation",
                                 character_info: Optional[Dict[str, Any]] = None) -> BlenderProject:
        """将游戏动作列表转换为Blender项目数据（MCP增强版）"""

        if self.use_mcp and self.mcp_manager:
            return self._convert_with_mcp(actions, project_name, character_info)
        else:
            # 回退到基础转换
            return super().convert_actions_to_blender(actions, project_name, character_info)
    
    def _convert_with_mcp(self, actions: List[Dict[str, Any]],
                         project_name: str,
                         character_info: Optional[Dict[str, Any]] = None) -> BlenderProject:
        """使用MCP进行增强转换"""
        logger.info(f"使用MCP增强转换 {len(actions)} 个动作")

        # 设置角色信息
        if character_info:
            from backend.characters.character_manager import character_manager
            self.character_info = character_manager.validate_character_info(character_info)
            self.character_config = character_manager.get_character_type(self.character_info["type"])
            logger.info(f"使用角色类型: {self.character_info['type']}")

        # 重置状态
        self.current_frame = 1
        self.animation_channels = []
        
        # 使用MCP创建高级动画
        try:
            # 为每个动作生成MCP代码
            for i, action in enumerate(actions):
                logger.info(f"处理动作 {i+1}/{len(actions)}: {action.get('action', 'unknown')}")
                self._convert_action_with_mcp(action, i)
            
            # 获取最终场景信息
            final_scene_info = self.mcp_manager.get_scene_info()
            total_frames = max(self.current_frame, 50)
            
            # 创建动画片段
            animation_clip = BlenderAnimationClip(
                name=f"{project_name}_Animation",
                start_frame=1,
                end_frame=total_frames,
                frame_rate=self.frame_rate,
                channels=self.animation_channels
            )
            
            # 创建场景
            scene = BlenderScene(
                scene_name=f"{project_name}_Scene",
                frame_rate=self.frame_rate,
                frame_start=1,
                frame_end=total_frames
            )
            
            # 创建导出设置
            default_fbx_path = get_output_file_path(f"{project_name}.fbx")
            export_settings = BlenderExportSettings(
                export_path=default_fbx_path,
                export_format="FBX"
            )
            
            # 创建项目元数据
            metadata = {
                "source": "MotionAgent_MCP_Enhanced",
                "actions_count": len(actions),
                "total_frames": total_frames,
                "mcp_enhanced": True,
                "scene_objects": final_scene_info.get("object_count", 0)
            }

            # 添加角色信息
            if hasattr(self, 'character_info') and self.character_info:
                metadata["character"] = self.character_info

            if hasattr(self, 'character_config') and self.character_config:
                metadata["character_config"] = {
                    "type": self.character_config["name"],
                    "display_name": self.character_config["display_name"],
                    "height": self.character_config["height"],
                    "animation_features": self.character_config["animation_features"]
                }

            # 创建项目
            project = BlenderProject(
                project_name=project_name,
                scene=scene,
                animation_clips=[animation_clip],
                export_settings=export_settings,
                metadata=metadata
            )
            
            logger.info(f"MCP增强转换完成，总帧数: {total_frames}")
            return project
            
        except Exception as e:
            logger.error(f"MCP转换失败，回退到基础转换: {e}")
            return super().convert_actions_to_blender(actions, project_name)
    
    def _convert_action_with_mcp(self, action: Dict[str, Any], action_index: int):
        """使用MCP转换单个动作"""
        action_type = action.get("action", action.get("action_type", "unknown"))
        params = action.get("params", action.get("parameters", {}))
        
        try:
            if action_type == "move":
                self._mcp_convert_move_action(action, params, action_index)
            elif action_type == "jump":
                self._mcp_convert_jump_action(action, params, action_index)
            elif action_type == "emotion":
                self._mcp_convert_emotion_action(action, params, action_index)
            elif action_type == "idle":
                self._mcp_convert_idle_action(action, params, action_index)
            elif action_type == "attack":
                self._mcp_convert_attack_action(action, params, action_index)
            elif action_type == "defend":
                self._mcp_convert_defend_action(action, params, action_index)
            elif action_type == "gesture":
                self._mcp_convert_gesture_action(action, params, action_index)
            elif action_type == "pose":
                self._mcp_convert_pose_action(action, params, action_index)
            else:
                # 使用基础转换作为回退
                self._convert_single_action(action)
                
        except Exception as e:
            logger.error(f"MCP动作转换失败 {action_type}: {e}")
            # 回退到基础转换
            self._convert_single_action(action)
    
    def _mcp_convert_move_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                action_index: int):
        """使用MCP转换移动动作"""
        direction = params.get("direction", "forward")
        speed = params.get("speed", 0.5)
        duration = params.get("duration", 1.0)
        
        # 计算移动参数
        distance = speed * duration * 5.0
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 生成MCP代码进行移动
        move_code = f"""
import bpy

# 获取角色对象
character = bpy.data.objects.get("{self.character_name}")
if character:
    # 设置关键帧
    character.location = character.location  # 当前位置
    character.keyframe_insert(data_path="location", frame={start_frame})
    
    # 计算新位置
    direction_map = {{
        "forward": (0, {distance}, 0),
        "backward": (0, -{distance}, 0),
        "left": (-{distance}, 0, 0),
        "right": ({distance}, 0, 0),
        "up": (0, 0, {distance}),
        "down": (0, 0, -{distance})
    }}
    
    offset = direction_map.get("{direction}", (0, {distance}, 0))
    character.location = (
        character.location.x + offset[0],
        character.location.y + offset[1],
        character.location.z + offset[2]
    )
    character.keyframe_insert(data_path="location", frame={end_frame})
    
    print(f"移动动作完成: {{character.location}}")
"""
        
        result = self.mcp_manager.execute_blender_code(move_code)
        if result.get("success"):
            logger.info(f"MCP移动动作执行成功: {direction}")

            # 同时创建动画通道记录
            direction_map = {
                "forward": (0, distance, 0),
                "backward": (0, -distance, 0),
                "left": (-distance, 0, 0),
                "right": (distance, 0, 0),
                "up": (0, 0, distance),
                "down": (0, 0, -distance)
            }

            offset = direction_map.get(direction, (0, distance, 0))
            keyframes = [
                BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
                BlenderKeyframe(frame=end_frame, value=list(offset))
            ]

            channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="location",
                animation_type=BlenderAnimationType.LOCATION,
                keyframes=keyframes
            )

            self.animation_channels.append(channel)
        else:
            logger.warning(f"MCP移动动作失败，使用基础转换: {result.get('error')}")
            self._convert_move_action(action, params)

        self.current_frame = end_frame
    
    def _mcp_convert_jump_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                action_index: int):
        """使用MCP转换跳跃动作"""
        height = params.get("height", 1.0)
        distance = params.get("distance", 1.0)
        style = params.get("style", "normal")
        
        jump_duration = 1.0 if style == "normal" else 1.5
        start_frame = self.current_frame
        peak_frame = self.current_frame + seconds_to_frames(jump_duration * 0.4, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(jump_duration, self.frame_rate)
        
        # 生成MCP跳跃代码
        jump_code = f"""
import bpy

character = bpy.data.objects.get("{self.character_name}")
if character:
    # 起始位置
    start_loc = character.location.copy()
    character.keyframe_insert(data_path="location", frame={start_frame})
    
    # 跳跃顶点
    character.location = (start_loc.x, start_loc.y + {distance * 0.5}, start_loc.z + {height})
    character.keyframe_insert(data_path="location", frame={peak_frame})
    
    # 落地位置
    character.location = (start_loc.x, start_loc.y + {distance}, start_loc.z)
    character.keyframe_insert(data_path="location", frame={end_frame})
    
    # 设置插值模式
    if character.animation_data and character.animation_data.action:
        for fcurve in character.animation_data.action.fcurves:
            if fcurve.data_path == "location":
                for keyframe in fcurve.keyframe_points:
                    if keyframe.co[0] == {peak_frame}:
                        keyframe.interpolation = 'EASE'
    
    print(f"跳跃动作完成: 高度{height}, 距离{distance}")
"""
        
        result = self.mcp_manager.execute_blender_code(jump_code)
        if result.get("success"):
            logger.info(f"MCP跳跃动作执行成功: 高度{height}, 距离{distance}")
        else:
            logger.warning(f"MCP跳跃动作失败，使用基础转换: {result.get('error')}")
            self._convert_jump_action(action, params)
        
        self.current_frame = end_frame
    
    def _mcp_convert_emotion_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                   action_index: int):
        """使用MCP转换情绪动作"""
        emotion = params.get("emotion", "happy")
        intensity = params.get("intensity", 0.7)
        
        duration = 2.0
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 生成情绪表达代码
        emotion_code = f"""
import bpy

character = bpy.data.objects.get("{self.character_name}")
if character:
    # 根据情绪调整材质颜色
    emotion_colors = {{
        "happy": (1.0, 0.8, 0.2, 1.0),  # 黄色
        "sad": (0.2, 0.2, 0.8, 1.0),    # 蓝色
        "angry": (0.8, 0.2, 0.2, 1.0),  # 红色
        "surprised": (0.8, 0.2, 0.8, 1.0), # 紫色
        "neutral": (0.8, 0.6, 0.4, 1.0)    # 默认色
    }}
    
    color = emotion_colors.get("{emotion}", emotion_colors["neutral"])
    
    if character.data.materials:
        mat = character.data.materials[0]
        if mat.use_nodes:
            principled = mat.node_tree.nodes.get("Principled BSDF")
            if principled:
                # 设置颜色关键帧
                principled.inputs[0].default_value = (0.8, 0.6, 0.4, 1.0)
                principled.inputs[0].keyframe_insert("default_value", frame={start_frame})
                
                principled.inputs[0].default_value = color
                principled.inputs[0].keyframe_insert("default_value", frame={start_frame + 10})
                
                principled.inputs[0].default_value = (0.8, 0.6, 0.4, 1.0)
                principled.inputs[0].keyframe_insert("default_value", frame={end_frame})
    
    # 添加轻微的缩放动画表示情绪强度
    scale_factor = 1.0 + {intensity} * 0.1
    character.scale = (1.0, 1.0, 1.0)
    character.keyframe_insert(data_path="scale", frame={start_frame})
    
    character.scale = (scale_factor, scale_factor, scale_factor)
    character.keyframe_insert(data_path="scale", frame={start_frame + 15})
    
    character.scale = (1.0, 1.0, 1.0)
    character.keyframe_insert(data_path="scale", frame={end_frame})
    
    print(f"情绪动作完成: {emotion}, 强度{intensity}")
"""
        
        result = self.mcp_manager.execute_blender_code(emotion_code)
        if result.get("success"):
            logger.info(f"MCP情绪动作执行成功: {emotion}")
        else:
            logger.warning(f"MCP情绪动作失败，使用基础转换: {result.get('error')}")
            self._convert_emotion_action(action, params)
        
        self.current_frame = end_frame
    
    def _mcp_convert_idle_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                action_index: int):
        """使用MCP转换待机动作"""
        duration = params.get("duration", 1.0)
        mood = params.get("mood", "relaxed")
        
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 生成呼吸动画代码
        idle_code = f"""
import bpy
import math

character = bpy.data.objects.get("{self.character_name}")
if character:
    # 创建呼吸动画
    breath_amplitude = 0.02 if "{mood}" == "relaxed" else 0.01
    base_z = character.location.z
    
    for frame in range({start_frame}, {end_frame}, 6):
        breath_offset = breath_amplitude * math.sin((frame - {start_frame}) * 0.2)
        character.location = (character.location.x, character.location.y, base_z + breath_offset)
        character.keyframe_insert(data_path="location", frame=frame)
    
    print(f"待机动作完成: {mood}, 持续{duration}秒")
"""
        
        result = self.mcp_manager.execute_blender_code(idle_code)
        if result.get("success"):
            logger.info(f"MCP待机动作执行成功: {mood}")
        else:
            logger.warning(f"MCP待机动作失败，使用基础转换: {result.get('error')}")
            self._convert_idle_action(action, params)
        
        self.current_frame = end_frame
    
    def _mcp_convert_attack_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                  action_index: int):
        """使用MCP转换攻击动作"""
        weapon = params.get("weapon", "fist")
        style = params.get("style", "slash")
        strength = params.get("strength", 0.8)
        
        duration = 0.8
        start_frame = self.current_frame
        strike_frame = self.current_frame + seconds_to_frames(duration * 0.3, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 生成攻击动画代码
        attack_code = f"""
import bpy
import math

character = bpy.data.objects.get("{self.character_name}")
if character:
    # 攻击动作：旋转和缩放组合
    rotation_amount = {strength} * math.pi / 2  # 90度 * 强度
    
    # 起始姿态
    character.rotation_euler = (0, 0, 0)
    character.keyframe_insert(data_path="rotation_euler", frame={start_frame})
    
    # 攻击姿态
    character.rotation_euler = (rotation_amount, 0, 0)
    character.keyframe_insert(data_path="rotation_euler", frame={strike_frame})
    
    # 恢复姿态
    character.rotation_euler = (0, 0, 0)
    character.keyframe_insert(data_path="rotation_euler", frame={end_frame})
    
    # 添加攻击时的轻微放大效果
    character.scale = (1.0, 1.0, 1.0)
    character.keyframe_insert(data_path="scale", frame={start_frame})
    
    scale_boost = 1.0 + {strength} * 0.2
    character.scale = (scale_boost, scale_boost, scale_boost)
    character.keyframe_insert(data_path="scale", frame={strike_frame})
    
    character.scale = (1.0, 1.0, 1.0)
    character.keyframe_insert(data_path="scale", frame={end_frame})
    
    print(f"攻击动作完成: {weapon}, {style}, 强度{strength}")
"""
        
        result = self.mcp_manager.execute_blender_code(attack_code)
        if result.get("success"):
            logger.info(f"MCP攻击动作执行成功: {weapon}, {style}")
        else:
            logger.warning(f"MCP攻击动作失败，使用基础转换: {result.get('error')}")
            self._convert_attack_action(action, params)
        
        self.current_frame = end_frame
    
    def _mcp_convert_defend_action(self, action: Dict[str, Any], params: Dict[str, Any], 
                                  action_index: int):
        """使用MCP转换防御动作"""
        defend_type = params.get("type", "block")
        direction = params.get("direction", "front")
        
        duration = 1.5
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 生成防御动画代码
        defend_code = f"""
import bpy

character = bpy.data.objects.get("{self.character_name}")
if character:
    # 防御姿态：下蹲和收缩
    base_location = character.location.copy()
    base_scale = character.scale.copy()
    
    # 起始姿态
    character.keyframe_insert(data_path="location", frame={start_frame})
    character.keyframe_insert(data_path="scale", frame={start_frame})
    
    # 防御姿态
    character.location = (base_location.x, base_location.y, base_location.z - 0.3)
    character.scale = (0.9, 0.9, 0.8)  # 轻微收缩
    character.keyframe_insert(data_path="location", frame={start_frame + 10})
    character.keyframe_insert(data_path="scale", frame={start_frame + 10})
    
    # 保持防御姿态
    character.keyframe_insert(data_path="location", frame={end_frame - 10})
    character.keyframe_insert(data_path="scale", frame={end_frame - 10})
    
    # 恢复姿态
    character.location = base_location
    character.scale = base_scale
    character.keyframe_insert(data_path="location", frame={end_frame})
    character.keyframe_insert(data_path="scale", frame={end_frame})
    
    print(f"防御动作完成: {defend_type}, 方向{direction}")
"""
        
        result = self.mcp_manager.execute_blender_code(defend_code)
        if result.get("success"):
            logger.info(f"MCP防御动作执行成功: {defend_type}")
        else:
            logger.warning(f"MCP防御动作失败，使用基础转换: {result.get('error')}")
            self._convert_defend_action(action, params)
        
        self.current_frame = end_frame

    def _mcp_convert_gesture_action(self, action: Dict[str, Any], params: Dict[str, Any],
                                   action_index: int):
        """使用MCP转换手势动作"""
        gesture_type = params.get("type", "wave")
        hand = params.get("hand", "right")
        duration = params.get("duration", 2.0)
        intensity = params.get("intensity", 0.7)

        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)

        # 生成手势动画代码
        gesture_code = f"""
import bpy
import math

character = bpy.data.objects.get("{self.character_name}")
if character and character.type == 'ARMATURE':
    # 进入姿势模式
    bpy.context.view_layer.objects.active = character
    bpy.ops.object.mode_set(mode='POSE')

    # 获取手臂骨骼
    arm_bone_name = "{hand.title()}Arm" if "{hand}" in ["left", "right"] else "RightArm"
    if arm_bone_name in character.pose.bones:
        arm_bone = character.pose.bones[arm_bone_name]

        # 挥手动作
        if "{gesture_type}" == "wave":
            # 创建挥手的周期性动作
            for i in range(0, int({duration} * 4)):
                frame = {start_frame} + i * 6
                if frame >= {end_frame}:
                    break

                angle = {intensity} * 45 * math.sin(i * math.pi / 2)
                arm_bone.rotation_euler = (0, math.radians(angle), 0)
                arm_bone.keyframe_insert(data_path="rotation_euler", frame=frame)

            # 结束时回到原位
            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame})

        elif "{gesture_type}" == "point":
            # 指向动作
            arm_bone.rotation_euler = (0, 0, math.radians(45))
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={start_frame + 10})
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame - 10})

            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame})

    # 回到对象模式
    bpy.ops.object.mode_set(mode='OBJECT')
    print(f"手势动作完成: {gesture_type}, 手: {hand}, 持续{duration}秒")
else:
    # 如果没有骨架，使用简单的对象旋转
    if character:
        if "{gesture_type}" == "wave":
            for i in range(0, int({duration} * 4)):
                frame = {start_frame} + i * 6
                if frame >= {end_frame}:
                    break

                angle = {intensity} * 15 * math.sin(i * math.pi / 2)
                character.rotation_euler = (0, 0, math.radians(angle))
                character.keyframe_insert(data_path="rotation_euler", frame=frame)

            character.rotation_euler = (0, 0, 0)
            character.keyframe_insert(data_path="rotation_euler", frame={end_frame})

        print(f"简化手势动作完成: {gesture_type}")
"""

        result = self.mcp_manager.execute_blender_code(gesture_code)
        if result.get("success"):
            logger.info(f"MCP手势动作执行成功: {gesture_type}")
        else:
            logger.warning(f"MCP手势动作失败，使用基础转换: {result.get('error')}")
            self._convert_gesture_action(action, params)

        self.current_frame = end_frame

    def _mcp_convert_pose_action(self, action: Dict[str, Any], params: Dict[str, Any],
                                action_index: int):
        """使用MCP转换姿势动作"""
        pose_type = params.get("pose_type", "confident")
        duration = params.get("duration", 3.0)

        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)

        # 生成姿势动画代码
        pose_code = f"""
import bpy
import math

character = bpy.data.objects.get("{self.character_name}")
if character and character.type == 'ARMATURE':
    # 进入姿势模式
    bpy.context.view_layer.objects.active = character
    bpy.ops.object.mode_set(mode='POSE')

    if "{pose_type}" == "victory":
        # 胜利姿势：双手举高
        for arm_name in ["LeftArm", "RightArm"]:
            if arm_name in character.pose.bones:
                arm_bone = character.pose.bones[arm_name]

                # 起始姿态
                arm_bone.rotation_euler = (0, 0, 0)
                arm_bone.keyframe_insert(data_path="rotation_euler", frame={start_frame})

                # 胜利姿态
                arm_bone.rotation_euler = (math.radians(45), 0, math.radians(30))
                arm_bone.keyframe_insert(data_path="rotation_euler", frame={start_frame + 10})
                arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame - 10})

                # 恢复姿态
                arm_bone.rotation_euler = (0, 0, 0)
                arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame})

    elif "{pose_type}" == "thinking":
        # 思考姿势：手托下巴
        if "RightArm" in character.pose.bones:
            arm_bone = character.pose.bones["RightArm"]

            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={start_frame})

            arm_bone.rotation_euler = (math.radians(90), 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={start_frame + 15})
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame - 15})

            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler", frame={end_frame})

    # 回到对象模式
    bpy.ops.object.mode_set(mode='OBJECT')
    print(f"姿势动作完成: {pose_type}, 持续{duration}秒")
else:
    # 简化版本
    if character:
        if "{pose_type}" == "victory":
            character.rotation_euler = (0, 0, math.radians(15))
            character.keyframe_insert(data_path="rotation_euler", frame={start_frame + 10})
            character.keyframe_insert(data_path="rotation_euler", frame={end_frame - 10})

            character.rotation_euler = (0, 0, 0)
            character.keyframe_insert(data_path="rotation_euler", frame={end_frame})

        print(f"简化姿势动作完成: {pose_type}")
"""

        result = self.mcp_manager.execute_blender_code(pose_code)
        if result.get("success"):
            logger.info(f"MCP姿势动作执行成功: {pose_type}")
        else:
            logger.warning(f"MCP姿势动作失败，使用基础转换: {result.get('error')}")
            self._convert_pose_action(action, params)

        self.current_frame = end_frame

    def get_scene_screenshot(self) -> Optional[bytes]:
        """获取场景截图"""
        if self.use_mcp and self.mcp_manager:
            try:
                return self.mcp_manager.get_viewport_screenshot()
            except Exception as e:
                logger.error(f"获取场景截图失败: {e}")
        return None
    
    def get_current_scene_info(self) -> Dict[str, Any]:
        """获取当前场景信息"""
        if self.use_mcp and self.mcp_manager:
            try:
                return self.mcp_manager.get_scene_info()
            except Exception as e:
                logger.error(f"获取场景信息失败: {e}")
        return {"error": "MCP not available"}
