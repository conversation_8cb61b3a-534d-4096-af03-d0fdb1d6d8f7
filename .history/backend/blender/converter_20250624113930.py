"""
游戏动作 JSON 到 Blender 动画数据转换器
"""

import json
import math
from typing import List, Dict, Any, Optional, Tuple
from backend.blender.animation_schema import (
    BlenderAnimationClip, BlenderAnimationChannel, BlenderKeyframe,
    BlenderProject, BlenderScene, BlenderExportSettings,
    BlenderAnimationType, InterpolationMode,
    ACTION_TO_BLENDER_MAPPING, DEFAULT_CHARACTER_BONES,
    seconds_to_frames, FRAME_RATE
)
from backend.schemas.action_command import ActionCommand, ActionType
from backend.utils.file import get_output_file_path
from backend.characters.character_manager import character_manager


class GameActionToBlenderConverter:
    """游戏动作到 Blender 动画转换器"""
    
    def __init__(self, character_name: str = "Character", frame_rate: float = FRAME_RATE):
        """初始化转换器

        Args:
            character_name: 角色名称
            frame_rate: 帧率
        """
        self.character_name = character_name
        self.frame_rate = frame_rate
        self.current_frame = 1
        self.animation_channels = []
        self.character_info = None
        self.character_config = None
        
    def convert_actions_to_blender(self, actions: List[Dict[str, Any]],
                                 project_name: str = "MotionAgentAnimation",
                                 character_info: Optional[Dict[str, Any]] = None) -> BlenderProject:
        """将游戏动作列表转换为 Blender 项目数据

        Args:
            actions: 游戏动作列表
            project_name: 项目名称
            character_info: 角色信息

        Returns:
            BlenderProject: Blender 项目数据
        """
        # 重置状态
        self.current_frame = 1
        self.animation_channels = []

        # 设置角色信息
        self.character_info = character_manager.validate_character_info(character_info)
        self.character_config = character_manager.get_character_type(self.character_info["type"])

        # 更新角色名称
        if character_info and character_info.get("name"):
            self.character_name = character_info["name"]
        
        # 转换每个动作
        for action in actions:
            self._convert_single_action(action)
        
        # 计算总帧数
        total_frames = max(self.current_frame, 50)  # 至少50帧
        
        # 创建动画片段
        animation_clip = BlenderAnimationClip(
            name=f"{project_name}_Animation",
            start_frame=1,
            end_frame=total_frames,
            frame_rate=self.frame_rate,
            channels=self.animation_channels
        )
        
        # 创建场景
        scene = BlenderScene(
            scene_name=f"{project_name}_Scene",
            frame_rate=self.frame_rate,
            frame_start=1,
            frame_end=total_frames
        )
        
        # 创建导出设置 - 使用 backend/output 目录
        default_fbx_path = get_output_file_path(f"{project_name}.fbx")
        export_settings = BlenderExportSettings(
            export_path=default_fbx_path,
            export_format="FBX"
        )
        
        # 创建项目元数据
        metadata = {
            "source": "MotionAgent",
            "actions_count": len(actions),
            "total_frames": total_frames,
            "character": self.character_info
        }

        # 如果有角色配置，添加角色详细信息
        if self.character_config:
            metadata["character_config"] = {
                "type": self.character_config["name"],
                "display_name": self.character_config["display_name"],
                "height": self.character_config["height"],
                "animation_features": self.character_config["animation_features"]
            }

        # 创建项目
        project = BlenderProject(
            project_name=project_name,
            scene=scene,
            animation_clips=[animation_clip],
            export_settings=export_settings,
            metadata=metadata
        )
        
        return project
    
    def _convert_single_action(self, action: Dict[str, Any]) -> None:
        """转换单个动作

        Args:
            action: 单个游戏动作
        """
        # 支持多种字段名格式
        action_type = action.get("action_type") or action.get("action")
        params = action.get("parameters", {}) or action.get("params", {})
        
        if action_type == "move":
            self._convert_move_action(action, params)
        elif action_type == "jump":
            self._convert_jump_action(action, params)
        elif action_type == "emotion":
            self._convert_emotion_action(action, params)
        elif action_type == "idle":
            self._convert_idle_action(action, params)
        elif action_type == "attack":
            self._convert_attack_action(action, params)
        elif action_type == "defend":
            self._convert_defend_action(action, params)
        elif action_type == "gesture":
            self._convert_gesture_action(action, params)
        elif action_type == "pose":
            self._convert_pose_action(action, params)
        else:
            # 默认处理：添加一个简单的等待
            duration = params.get("duration", 1.0)
            self.current_frame += seconds_to_frames(duration, self.frame_rate)
    
    def _convert_move_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换移动动作"""
        direction = params.get("direction", "forward")
        speed = params.get("speed", 0.5)
        duration = params.get("duration", 1.0)
        
        # 获取方向映射
        direction_mapping = ACTION_TO_BLENDER_MAPPING["move"].get(direction)
        if not direction_mapping:
            return
        
        # 计算移动距离
        distance = speed * duration * 5.0  # 缩放因子
        
        # 创建关键帧
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 根据轴向创建动画通道
        axis = direction_mapping["axis"]
        multiplier = direction_mapping["multiplier"]
        
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=end_frame, value=[
                distance * multiplier if axis == "x" else 0,
                distance * multiplier if axis == "y" else 0,
                distance * multiplier if axis == "z" else 0
            ])
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=keyframes
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame
    
    def _convert_jump_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换跳跃动作"""
        height = params.get("height", 1.0)
        distance = params.get("distance", 1.0)
        style = params.get("style", "normal")
        
        # 跳跃持续时间（秒）
        jump_duration = 1.0 if style == "normal" else 1.5
        
        start_frame = self.current_frame
        peak_frame = self.current_frame + seconds_to_frames(jump_duration * 0.4, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(jump_duration, self.frame_rate)
        
        # Z轴（高度）动画
        z_keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=peak_frame, value=[0, 0, height], 
                          interpolation=InterpolationMode.EASE_OUT),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0],
                          interpolation=InterpolationMode.EASE_IN)
        ]
        
        z_channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=z_keyframes
        )
        
        self.animation_channels.append(z_channel)
        
        # 如果有前进距离，添加Y轴动画
        if distance > 0:
            y_keyframes = [
                BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
                BlenderKeyframe(frame=end_frame, value=[0, distance, 0])
            ]
            
            y_channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="location",
                animation_type=BlenderAnimationType.LOCATION,
                keyframes=y_keyframes
            )
            
            self.animation_channels.append(y_channel)
        
        self.current_frame = end_frame
    
    def _convert_emotion_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换情绪动作"""
        emotion = params.get("emotion", "happy")
        intensity = params.get("intensity", 0.7)
        
        # 情绪持续时间
        duration = 2.0
        
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 获取情绪映射
        emotion_mapping = ACTION_TO_BLENDER_MAPPING["emotion"].get(emotion)
        if emotion_mapping and "shape_keys" in emotion_mapping:
            for shape_key, value in emotion_mapping["shape_keys"].items():
                keyframes = [
                    BlenderKeyframe(frame=start_frame, value=[0]),
                    BlenderKeyframe(frame=start_frame + 5, value=[value * intensity]),
                    BlenderKeyframe(frame=end_frame - 5, value=[value * intensity]),
                    BlenderKeyframe(frame=end_frame, value=[0])
                ]
                
                channel = BlenderAnimationChannel(
                    target_object=self.character_name,
                    target_property=f'key_blocks["{shape_key}"].value',
                    animation_type=BlenderAnimationType.SHAPE_KEY,
                    keyframes=keyframes
                )
                
                self.animation_channels.append(channel)
        
        self.current_frame = end_frame
    
    def _convert_idle_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换待机动作"""
        duration = params.get("duration", 1.0)
        mood = params.get("mood", "relaxed")
        
        # 简单的呼吸动画
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 轻微的上下浮动
        breath_amplitude = 0.02 if mood == "relaxed" else 0.01
        
        keyframes = []
        for frame in range(start_frame, end_frame, 6):  # 每6帧一个关键帧
            breath_offset = breath_amplitude * math.sin((frame - start_frame) * 0.2)
            keyframes.append(BlenderKeyframe(frame=frame, value=[0, 0, breath_offset]))
        
        if keyframes:
            channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="location",
                animation_type=BlenderAnimationType.LOCATION,
                keyframes=keyframes
            )
            
            self.animation_channels.append(channel)
        
        self.current_frame = end_frame
    
    def _convert_attack_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换攻击动作"""
        weapon = params.get("weapon", "fist")
        style = params.get("style", "slash")
        strength = params.get("strength", 0.8)
        
        # 攻击动作持续时间
        duration = 0.8
        
        start_frame = self.current_frame
        strike_frame = self.current_frame + seconds_to_frames(duration * 0.3, self.frame_rate)
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 手臂旋转动画（简化版）
        rotation_amount = strength * 90  # 度数
        
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=strike_frame, value=[math.radians(rotation_amount), 0, 0],
                          interpolation=InterpolationMode.EASE_OUT),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0],
                          interpolation=InterpolationMode.EASE_IN)
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="rotation_euler",
            animation_type=BlenderAnimationType.ROTATION,
            keyframes=keyframes,
            bone_name="RightArm"
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame
    
    def _convert_defend_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换防御动作"""
        defend_type = params.get("type", "block")
        direction = params.get("direction", "front")
        
        # 防御动作持续时间
        duration = 1.5
        
        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)
        
        # 防御姿态：轻微下蹲
        keyframes = [
            BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
            BlenderKeyframe(frame=start_frame + 10, value=[0, 0, -0.3]),
            BlenderKeyframe(frame=end_frame - 10, value=[0, 0, -0.3]),
            BlenderKeyframe(frame=end_frame, value=[0, 0, 0])
        ]
        
        channel = BlenderAnimationChannel(
            target_object=self.character_name,
            target_property="location",
            animation_type=BlenderAnimationType.LOCATION,
            keyframes=keyframes
        )
        
        self.animation_channels.append(channel)
        self.current_frame = end_frame

    def _convert_gesture_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换手势动作"""
        gesture_type = params.get("type", "wave")
        hand = params.get("hand", "right")
        duration = params.get("duration", 2.0)
        intensity = params.get("intensity", 0.7)

        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)

        # 手势动画：手臂旋转
        if gesture_type == "wave":
            # 挥手动作：Y轴旋转
            wave_amplitude = intensity * 45  # 度数
            keyframes = []

            # 创建挥手的周期性动作
            for i in range(0, int(duration * 4)):  # 每秒4次挥手
                frame = start_frame + i * 6  # 每6帧一个关键帧
                if frame >= end_frame:
                    break
                angle = wave_amplitude * math.sin(i * math.pi / 2)
                keyframes.append(BlenderKeyframe(
                    frame=frame,
                    value=[0, math.radians(angle), 0]
                ))

            # 结束时回到原位
            keyframes.append(BlenderKeyframe(frame=end_frame, value=[0, 0, 0]))

            # 选择手臂
            arm_name = f"{hand.title()}Arm" if hand in ["left", "right"] else "RightArm"

            channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="rotation_euler",
                animation_type=BlenderAnimationType.ROTATION,
                keyframes=keyframes,
                bone_name=arm_name
            )

            self.animation_channels.append(channel)

        self.current_frame = end_frame

    def _convert_pose_action(self, action: Dict[str, Any], params: Dict[str, Any]) -> None:
        """转换姿势动作"""
        pose_type = params.get("pose_type", "confident")
        duration = params.get("duration", 3.0)

        start_frame = self.current_frame
        end_frame = self.current_frame + seconds_to_frames(duration, self.frame_rate)

        # 根据姿势类型设置不同的动作
        if pose_type == "victory":
            # 胜利姿势：双手举高
            keyframes = [
                BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
                BlenderKeyframe(frame=start_frame + 10, value=[math.radians(45), 0, math.radians(30)]),
                BlenderKeyframe(frame=end_frame - 10, value=[math.radians(45), 0, math.radians(30)]),
                BlenderKeyframe(frame=end_frame, value=[0, 0, 0])
            ]

            # 左右手臂
            for arm in ["LeftArm", "RightArm"]:
                channel = BlenderAnimationChannel(
                    target_object=self.character_name,
                    target_property="rotation_euler",
                    animation_type=BlenderAnimationType.ROTATION,
                    keyframes=keyframes,
                    bone_name=arm
                )
                self.animation_channels.append(channel)

        elif pose_type == "thinking":
            # 思考姿势：手托下巴
            keyframes = [
                BlenderKeyframe(frame=start_frame, value=[0, 0, 0]),
                BlenderKeyframe(frame=start_frame + 15, value=[math.radians(90), 0, 0]),
                BlenderKeyframe(frame=end_frame - 15, value=[math.radians(90), 0, 0]),
                BlenderKeyframe(frame=end_frame, value=[0, 0, 0])
            ]

            channel = BlenderAnimationChannel(
                target_object=self.character_name,
                target_property="rotation_euler",
                animation_type=BlenderAnimationType.ROTATION,
                keyframes=keyframes,
                bone_name="RightArm"
            )
            self.animation_channels.append(channel)

        self.current_frame = end_frame
