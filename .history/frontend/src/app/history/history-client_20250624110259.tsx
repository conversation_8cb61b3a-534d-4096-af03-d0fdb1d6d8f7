'use client';

import { useState, useEffect } from 'react';
import AnimationPreview from '@/components/animation-preview';

interface FBXFile {
  name: string;
  path: string;
  size: number;
  modified: string;
  download_url: string;
}

export default function HistoryClient() {
  const [selectedFBXFile, setSelectedFBXFile] = useState<string>('');
  const [fbxFiles, setFbxFiles] = useState<FBXFile[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<FBXFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modified'>('modified');
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [showBatchActions, setShowBatchActions] = useState(false);

  const API_BASE_URL = 'http://localhost:8000';

  // 获取 FBX 文件列表
  const fetchFBXFiles = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/output-files?file_type=fbx`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // 为每个文件添加完整的 URL
      const filesWithFullUrls = (data.files || []).map((file: FBXFile) => ({
        ...file,
        download_url: `${API_BASE_URL}${file.download_url}`
      }));

      setFbxFiles(filesWithFullUrls);
      setFilteredFiles(filesWithFullUrls);

      // 如果有文件且没有选中的文件，自动选择第一个
      if (filesWithFullUrls.length > 0 && !selectedFBXFile) {
        setSelectedFBXFile(filesWithFullUrls[0].download_url);
      }
    } catch (err) {
      console.error('Error fetching FBX files:', err);
      setError('加载FBX文件失败，请确保后端服务正在运行。');
      setFbxFiles([]);
    } finally {
      setLoading(false);
    }
  };

  // 搜索和排序功能
  useEffect(() => {
    let filtered = fbxFiles.filter(file => 
      file.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return b.size - a.size;
        case 'modified':
          return new Date(b.modified).getTime() - new Date(a.modified).getTime();
        default:
          return 0;
      }
    });

    setFilteredFiles(filtered);
  }, [fbxFiles, searchTerm, sortBy]);

  // 页面加载时获取文件列表
  useEffect(() => {
    fetchFBXFiles();
  }, []);

  const selectedFile = filteredFiles.find(f => f.download_url === selectedFBXFile);

  const handleSelectFile = (filePath: string, checked: boolean) => {
    const newSelected = new Set(selectedFiles);
    if (checked) {
      newSelected.add(filePath);
    } else {
      newSelected.delete(filePath);
    }
    setSelectedFiles(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(new Set(filteredFiles.map(f => f.path)));
    } else {
      setSelectedFiles(new Set());
    }
  };

  const downloadSelectedFiles = () => {
    selectedFiles.forEach(filePath => {
      const file = fbxFiles.find(f => f.path === filePath);
      if (file) {
        const link = document.createElement('a');
        link.href = file.download_url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    });
  };

  return (
    <div className="space-y-8">
      {/* File Management Section */}
      <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            文件管理
          </h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowBatchActions(!showBatchActions)}
              className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              {showBatchActions ? '隐藏批量操作' : '显示批量操作'}
            </button>
            <button
              onClick={fetchFBXFiles}
              disabled={loading}
              className="inline-flex items-center rounded-lg bg-blue-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  加载中...
                </>
              ) : (
                '刷新列表'
              )}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 rounded-lg bg-red-50 border border-red-200 p-3">
            <span className="text-red-800">{error}</span>
          </div>
        )}

        {/* Search and Sort Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              搜索文件
            </label>
            <input
              id="search"
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="输入文件名进行搜索..."
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="sort" className="block text-sm font-medium text-gray-700 mb-2">
              排序方式
            </label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'size' | 'modified')}
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="modified">修改时间（最新优先）</option>
              <option value="name">文件名（A-Z）</option>
              <option value="size">文件大小（大到小）</option>
            </select>
          </div>
        </div>

        {/* Batch Actions */}
        {showBatchActions && (
          <div className="mb-6 rounded-lg bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedFiles.size === filteredFiles.length && filteredFiles.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">全选 ({selectedFiles.size} / {filteredFiles.length})</span>
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={downloadSelectedFiles}
                  disabled={selectedFiles.size === 0}
                  className="inline-flex items-center rounded-lg bg-green-600 px-3 py-1 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下载选中文件 ({selectedFiles.size})
                </button>
                <button
                  onClick={() => setSelectedFiles(new Set())}
                  disabled={selectedFiles.size === 0}
                  className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  清空选择
                </button>
              </div>
            </div>
          </div>
        )}

        {/* File List/Selector */}
        {showBatchActions ? (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文件列表（点击文件名预览，勾选复选框进行批量操作）
            </label>
            <div className="max-h-64 overflow-y-auto rounded-lg border border-gray-300">
              {filteredFiles.map((file) => (
                <div key={file.path} className="flex items-center p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    checked={selectedFiles.has(file.path)}
                    onChange={(e) => handleSelectFile(file.path, e.target.checked)}
                    className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <button
                    onClick={() => setSelectedFBXFile(file.download_url)}
                    className={`flex-1 text-left hover:text-blue-600 transition-colors ${selectedFBXFile === file.download_url ? 'text-blue-600 font-medium' : 'text-gray-900'}`}
                  >
                    <div className="font-medium">{file.name}</div>
                    <div className="text-sm text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} MB - {new Date(file.modified).toLocaleDateString()}
                    </div>
                  </button>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="mb-4">
            <label htmlFor="fbx-select" className="block text-sm font-medium text-gray-700 mb-2">
              选择要预览的FBX文件
            </label>
            <select
              id="fbx-select"
              value={selectedFBXFile}
              onChange={(e) => setSelectedFBXFile(e.target.value)}
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              disabled={loading || filteredFiles.length === 0}
            >
              <option value="">
                {loading ? '加载文件中...' : filteredFiles.length === 0 ? '未找到FBX文件' : '请选择一个FBX文件'}
              </option>
              {filteredFiles.map((file) => (
                <option key={file.path} value={file.download_url}>
                  {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB) - {new Date(file.modified).toLocaleDateString()}
                </option>
              ))}
            </select>
          </div>
        )}

        {fbxFiles.length > 0 && (
          <div className="text-sm text-gray-600">
            {searchTerm ? (
              <>显示 {filteredFiles.length} / {fbxFiles.length} 个文件</>
            ) : (
              <>共找到 {fbxFiles.length} 个FBX文件</>
            )}
          </div>
        )}
      </div>

      {/* Animation Preview Section */}
      <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          动画预览
        </h2>

        {selectedFBXFile ? (
          <AnimationPreview
            fbxUrl={selectedFBXFile}
            animationName={selectedFile?.name || "Selected Animation"}
            autoPlay={true}
            showGrid={true}
            showStats={false}
            cameraPosition={[5, 5, 5]}
            backgroundColor="#f8fafc"
          />
        ) : (
          <div className="h-96 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-center text-gray-500">
              <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h12V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V9z" />
                </svg>
              </div>
              <p className="text-lg font-medium mb-2">未选择FBX文件</p>
              <p className="text-sm">请从上方选择一个FBX文件来预览动画</p>
            </div>
          </div>
        )}
      </div>

      {/* File Information and Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* File Information */}
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            文件信息
          </h3>
          {selectedFile ? (
            <div className="space-y-3">
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="font-medium text-gray-700">文件名</span>
                <span className="text-gray-900">{selectedFile.name}</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="font-medium text-gray-700">文件大小</span>
                <span className="text-gray-900">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</span>
              </div>
              <div className="flex justify-between py-2 border-b border-gray-100">
                <span className="font-medium text-gray-700">修改时间</span>
                <span className="text-gray-900">{new Date(selectedFile.modified).toLocaleString()}</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium text-gray-700">文件路径</span>
                <span className="text-gray-900 text-sm break-all">{selectedFile.path}</span>
              </div>
              <div className="pt-4">
                <a
                  href={selectedFile.download_url}
                  download={selectedFile.name}
                  className="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  下载文件
                </a>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-gray-500">请选择文件以查看详细信息</p>
            </div>
          )}
        </div>

        {/* 3D Viewer Controls */}
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            3D查看器控制
          </h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900">左键拖拽</div>
                <div className="text-sm text-gray-600">旋转相机视角</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900">右键拖拽</div>
                <div className="text-sm text-gray-600">平移相机位置</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900">滚轮</div>
                <div className="text-sm text-gray-600">缩放视图</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15" />
                </svg>
              </div>
              <div>
                <div className="font-medium text-gray-900">自动播放</div>
                <div className="text-sm text-gray-600">动画循环播放</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          文件统计
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-1">{fbxFiles.length}</div>
            <div className="text-sm text-gray-600">总文件数</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">{filteredFiles.length}</div>
            <div className="text-sm text-gray-600">显示文件数</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-1">
              {fbxFiles.length > 0 ? (fbxFiles.reduce((sum, file) => sum + file.size, 0) / 1024 / 1024).toFixed(1) : '0'}
            </div>
            <div className="text-sm text-gray-600">总大小 (MB)</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-1">
              {fbxFiles.length > 0 ? (fbxFiles.reduce((sum, file) => sum + file.size, 0) / fbxFiles.length / 1024 / 1024).toFixed(1) : '0'}
            </div>
            <div className="text-sm text-gray-600">平均大小 (MB)</div>
          </div>
        </div>
      </div>
    </div>
  );
}
