'use client';

import { useState } from 'react';
import AnimationPreview from '@/components/animation-preview';

interface GeneratedFBX {
  url: string;
  name: string;
  actions: any[];
}

export default function ConversationClient() {
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedFBX, setGeneratedFBX] = useState<GeneratedFBX | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('');

  const API_BASE_URL = 'http://localhost:8000';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputText.trim()) return;

    setLoading(true);
    setError(null);
    setSuccess(null);
    setCurrentStep('正在解析自然语言...');

    try {
      // 第一步：生成动作
      const actionsResponse = await fetch(`${API_BASE_URL}/parse`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: inputText,
          use_llm: true,
          model_name: "gpt-4o-mini",
          temperature: 0.1
        }),
      });

      if (!actionsResponse.ok) {
        throw new Error(`生成动作失败: ${actionsResponse.status}`);
      }

      const actionsData = await actionsResponse.json();
      
      if (!actionsData.success || !actionsData.actions || actionsData.actions.length === 0) {
        throw new Error('生成的动作为空或无效');
      }

      setCurrentStep('正在生成FBX文件...');

      // 第二步：导出FBX
      const fbxResponse = await fetch(`${API_BASE_URL}/export-fbx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          actions: actionsData.actions,
          project_name: `Conversation_${Date.now()}`,
          character_name: "小女孩",
          output_filename: `conversation_${Date.now()}.fbx`
        }),
      });

      if (!fbxResponse.ok) {
        throw new Error(`导出FBX失败: ${fbxResponse.status}`);
      }

      const fbxData = await fbxResponse.json();

      if (!fbxData.export_success || !fbxData.download_url) {
        throw new Error('FBX导出失败或下载链接无效');
      }

      // 设置生成的FBX信息
      setGeneratedFBX({
        url: `${API_BASE_URL}${fbxData.download_url}`,
        name: fbxData.output_file_path?.split('/').pop() || 'Generated Animation',
        actions: actionsData.actions
      });

      setCurrentStep('');
      setSuccess(`成功生成动画！包含 ${actionsData.actions.length} 个动作`);

    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : '生成动画时发生未知错误');
      setCurrentStep('');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setInputText('');
    setGeneratedFBX(null);
    setError(null);
    setSuccess(null);
  };

  const exampleTexts = [
    "一个小女孩挥手3秒，然后向前五步走，并跳跃，向左移动10步，然后蹲下",
    "角色向前跑步5秒，然后停下来挥手打招呼",
    "角色蹲下2秒，然后站起来跳跃3次"
  ];

  return (
    <div className="space-y-8">
      {/* Input Section */}
      <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          动作描述输入
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="action-input" className="block text-sm font-medium text-gray-700 mb-2">
              请描述您想要的动作序列
            </label>
            <textarea
              id="action-input"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="例如：一个小女孩挥手3秒，然后向前五步走..."
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
              rows={4}
              disabled={loading}
            />
          </div>

          <div className="flex space-x-3">
            <button
              type="submit"
              disabled={loading || !inputText.trim()}
              className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  生成中...
                </>
              ) : (
                '生成动画'
              )}
            </button>

            <button
              type="button"
              onClick={handleClear}
              disabled={loading}
              className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              清空
            </button>
          </div>
        </form>

        {/* Status Messages */}
        {loading && currentStep && (
          <div className="mt-4 rounded-lg bg-blue-50 border border-blue-200 p-3 flex items-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-blue-800"><strong>进度：</strong> {currentStep}</span>
          </div>
        )}

        {error && (
          <div className="mt-4 rounded-lg bg-red-50 border border-red-200 p-3">
            <span className="text-red-800"><strong>错误：</strong> {error}</span>
          </div>
        )}

        {success && (
          <div className="mt-4 rounded-lg bg-green-50 border border-green-200 p-3">
            <span className="text-green-800"><strong>成功：</strong> {success}</span>
          </div>
        )}
      </div>

      {/* Preview Section */}
      <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          实时预览
        </h2>

        {generatedFBX ? (
          <div className="space-y-4">
            <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
              <p><strong>文件名：</strong> {generatedFBX.name}</p>
              <p><strong>动作数量：</strong> {generatedFBX.actions.length}</p>
            </div>
            
            <AnimationPreview
              fbxUrl={generatedFBX.url}
              animationName={generatedFBX.name}
              autoPlay={true}
              showGrid={true}
              showStats={false}
              cameraPosition={[5, 5, 5]}
              backgroundColor="#f8fafc"
            />
          </div>
        ) : (
          <div className="h-96 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <div className="text-center text-gray-500">
              <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h12V6H6zm3 3a1 1 0 112 0v6a1 1 0 11-2 0V9zm4 0a1 1 0 112 0v6a1 1 0 11-2 0V9z" />
                </svg>
              </div>
              <p className="text-lg font-medium mb-2">暂无预览内容</p>
              <p className="text-sm">请在上方输入动作描述并点击"生成动画"来预览结果</p>
            </div>
          </div>
        )}
      </div>

      {/* Examples Section */}
      <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          示例文本
        </h3>
        <div className="grid gap-3 sm:grid-cols-1">
          {exampleTexts.map((text, index) => (
            <button
              key={index}
              onClick={() => setInputText(text)}
              className="text-left p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all duration-200"
            >
              <div className="text-sm text-gray-700">
                "{text}"
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
