'use client';

import { useState, useEffect } from 'react';

interface HealthStatus {
  status: string;
  components: {
    action_parser: boolean;
    game_agent: boolean;
    fbx_exporter: boolean;
    addon_manager: boolean;
    blender: boolean;
    mcp_enabled: boolean;
    mcp_server_running: boolean;
  };
  blender_info: {
    available: boolean;
    version?: string;
    path?: string;
    platform?: string;
  };
  mcp_status: {
    enabled: boolean;
    server_running: boolean;
    server_url?: string;
  };
}

interface SystemInfo {
  message: string;
  version: string;
  status: string;
  llm_available: boolean;
}

export default function StatusClient() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30);

  const API_BASE_URL = 'http://localhost:8000';

  const fetchStatus = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 获取健康状态
      const healthResponse = await fetch(`${API_BASE_URL}/health`);
      if (!healthResponse.ok) {
        throw new Error(`健康检查失败: ${healthResponse.status}`);
      }
      const healthData = await healthResponse.json();
      setHealthStatus(healthData);

      // 获取系统信息
      const systemResponse = await fetch(`${API_BASE_URL}/`);
      if (!systemResponse.ok) {
        throw new Error(`系统信息获取失败: ${systemResponse.status}`);
      }
      const systemData = await systemResponse.json();
      setSystemInfo(systemData);

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching status:', err);
      setError(err instanceof Error ? err.message : '获取状态信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(fetchStatus, refreshInterval * 1000);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const getStatusColor = (status: boolean) => {
    return status ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: boolean) => {
    return status ? '✅' : '❌';
  };

  const getStatusText = (status: boolean) => {
    return status ? '正常' : '异常';
  };

  const getStatusBadgeColor = (status: boolean) => {
    return status 
      ? 'bg-green-100 text-green-800 border-green-200' 
      : 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="space-y-8">
      {/* Control Panel */}
      <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            控制面板
          </h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">自动刷新</label>
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              {autoRefresh && (
                <select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  className="text-sm rounded-lg border border-gray-300 px-2 py-1 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value={10}>10秒</option>
                  <option value={30}>30秒</option>
                  <option value={60}>1分钟</option>
                  <option value={300}>5分钟</option>
                </select>
              )}
            </div>
            <button
              onClick={fetchStatus}
              disabled={loading}
              className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  刷新中...
                </>
              ) : (
                '手动刷新'
              )}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 rounded-lg bg-red-50 border border-red-200 p-3">
            <span className="text-red-800">{error}</span>
          </div>
        )}

        <div className="flex items-center justify-between text-sm">
          {lastUpdated && (
            <div className="text-gray-600">
              最后更新: {lastUpdated.toLocaleString()}
            </div>
          )}
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${error ? 'bg-red-500' : 'bg-green-500'}`}></div>
            <span className={error ? 'text-red-600' : 'text-green-600'}>
              {error ? '连接异常' : '连接正常'}
            </span>
            {autoRefresh && !error && (
              <span className="text-gray-500">
                (自动刷新: {refreshInterval}秒)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* System Overview */}
      {systemInfo && (
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">系统概览</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">{systemInfo.version}</div>
              <div className="text-sm text-gray-600">版本号</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">{systemInfo.status}</div>
              <div className="text-sm text-gray-600">运行状态</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {getStatusIcon(systemInfo.llm_available)}
              </div>
              <div className="text-sm text-gray-600">LLM可用性</div>
            </div>
            <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-1">
                {healthStatus?.status === 'healthy' ? '✅' : '❌'}
              </div>
              <div className="text-sm text-gray-600">整体健康</div>
            </div>
          </div>
        </div>
      )}

      {/* Component Status */}
      {healthStatus && (
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">组件状态</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(healthStatus.components).map(([key, status]) => (
              <div key={key} className="flex items-center justify-between p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${status ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="font-medium text-gray-900">
                    {key === 'action_parser' && '动作解析器'}
                    {key === 'game_agent' && '游戏代理'}
                    {key === 'fbx_exporter' && 'FBX导出器'}
                    {key === 'addon_manager' && '插件管理器'}
                    {key === 'blender' && 'Blender'}
                    {key === 'mcp_enabled' && 'MCP启用'}
                    {key === 'mcp_server_running' && 'MCP服务器'}
                  </span>
                </div>
                <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeColor(status)}`}>
                  {getStatusText(status)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Blender Information */}
      {healthStatus?.blender_info && (
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Blender信息</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
              <span className="font-medium text-gray-700">可用性</span>
              <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeColor(healthStatus.blender_info.available)}`}>
                {getStatusIcon(healthStatus.blender_info.available)} {getStatusText(healthStatus.blender_info.available)}
              </span>
            </div>
            {healthStatus.blender_info.version && (
              <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
                <span className="font-medium text-gray-700">版本</span>
                <span className="text-gray-900 font-mono">{healthStatus.blender_info.version}</span>
              </div>
            )}
            {healthStatus.blender_info.path && (
              <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
                <span className="font-medium text-gray-700">安装路径</span>
                <span className="text-gray-900 text-sm font-mono break-all max-w-md text-right">{healthStatus.blender_info.path}</span>
              </div>
            )}
            {healthStatus.blender_info.platform && (
              <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
                <span className="font-medium text-gray-700">平台</span>
                <span className="text-gray-900">{healthStatus.blender_info.platform}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* MCP Status */}
      {healthStatus?.mcp_status && (
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">MCP状态</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
              <span className="font-medium text-gray-700">MCP启用</span>
              <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeColor(healthStatus.mcp_status.enabled)}`}>
                {getStatusIcon(healthStatus.mcp_status.enabled)} {getStatusText(healthStatus.mcp_status.enabled)}
              </span>
            </div>
            <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
              <span className="font-medium text-gray-700">服务器运行</span>
              <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeColor(healthStatus.mcp_status.server_running)}`}>
                {getStatusIcon(healthStatus.mcp_status.server_running)} {getStatusText(healthStatus.mcp_status.server_running)}
              </span>
            </div>
            {healthStatus.mcp_status.server_url && (
              <div className="flex items-center justify-between p-4 rounded-lg bg-gray-50">
                <span className="font-medium text-gray-700">服务器URL</span>
                <span className="text-gray-900 font-mono text-sm">{healthStatus.mcp_status.server_url}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* API Endpoints */}
      <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">API端点</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              核心功能
            </h4>
            <ul className="space-y-2">
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-green-600 font-mono">POST</span>
                <span>/parse - 生成动作命令</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-green-600 font-mono">POST</span>
                <span>/export-fbx - 导出FBX文件</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-blue-600 font-mono">GET</span>
                <span>/health - 健康检查</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-blue-600 font-mono">GET</span>
                <span>/output-files - 文件列表</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
              MCP功能
            </h4>
            <ul className="space-y-2">
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-green-600 font-mono">POST</span>
                <span>/mcp-server - MCP服务器管理</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-blue-600 font-mono">GET</span>
                <span>/scene-info - 场景信息</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-green-600 font-mono">POST</span>
                <span>/execute-blender-code - 执行代码</span>
              </li>
              <li className="flex items-center text-sm text-gray-700">
                <span className="w-16 text-green-600 font-mono">POST</span>
                <span>/manage-addon - 插件管理</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
