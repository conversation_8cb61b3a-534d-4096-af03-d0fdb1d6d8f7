import { Metadata } from 'next';
import StatusClient from './status-client';
import PageTransition from '@/components/page-transition';

export const metadata: Metadata = {
  title: '状态 - Motion Agent',
  description: '监控系统状态和各组件运行情况',
};

export default function StatusPage() {
  return (
    <PageTransition>
      <div className="mx-auto max-w-7xl px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            系统状态监控
          </h1>
          <p className="mt-4 text-lg text-gray-600">
            实时监控后端服务、组件状态和系统健康情况
          </p>
        </div>
        <StatusClient />
      </div>
    </PageTransition>
  );
}
