import { Metadata } from 'next';
import ConversationClient from './conversation-client';
import PageTransition from '@/components/page-transition';

export const metadata: Metadata = {
  title: '对话 - Motion Agent',
  description: '使用自然语言描述动作，AI将自动生成3D动画',
};

export default function ConversationPage() {
  return (
    <PageTransition>
      <div className="mx-auto max-w-7xl px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            AI动作生成对话
          </h1>
          <p className="mt-4 text-lg text-gray-600">
            使用自然语言描述您想要的动作序列，AI将自动解析并生成3D动画文件
          </p>
        </div>
        <ConversationClient />
      </div>
    </PageTransition>
  );
}
